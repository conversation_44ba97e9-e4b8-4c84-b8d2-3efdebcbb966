"use client";

import { useState } from "react";
import AuthModal from "@/components/AuthModal";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, Target, DollarSign } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const benefits = [
  {
    icon: Upload,
    title: "Show Up Inside AI Platforms",
    description:
      "Get your product discovered in tools like ChatGPT, Claude, Perplexity, and more — right when users ask.",
  },
  {
    icon: Target,
    title: "Intent-Triggered Exposure",
    description:
      "Your offer appears only when users express interest in your category. No irrelevant impressions.",
  },
  {
    icon: DollarSign,
    title: "Only Pay for Results",
    description:
      "You’re only charged when someone clicks, installs, or converts — never for passive views.",
  },
];

export default function ForBrands() {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  return (
    <section
      id="for-brands"
      className="w-full bg-gradient-to-b from-indigo-100 to-indigo-50 py-32"
    >
      <div className="mx-auto max-w-7xl px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-extrabold text-gray-800 tracking-tight mb-4">
            Promote Your Brand in AI Platforms
          </h2>
          <p className="text-sm text-indigo-600 uppercase tracking-wide font-medium mb-4">
            Built for forward-thinking SaaS & startups
          </p>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            AdMesh lets your brand show up natively inside AI tools like ChatGPT, Claude, and Perplexity — whenever users express intent.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, idx) => {
            const Icon = benefit.icon;
            return (
              <Card
                key={idx}
                className="group h-full border-2 border-transparent bg-white transition-all duration-300 transform hover:scale-[1.03] hover:shadow-xl hover:border-indigo-500"
              >
                <CardHeader className="space-y-4 text-center">
                  <div className="bg-indigo-100 group-hover:bg-indigo-600 w-16 h-16 rounded-full flex items-center justify-center mb-4 shadow-md transition-colors duration-300">
                    <Icon className="h-8 w-8 text-indigo-600 group-hover:text-white transition-colors duration-300" />
                  </div>
                  <CardTitle className="text-xl font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">
                    {benefit.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="px-6 py-4">
                  <p className="text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="mt-12 text-center">
          <Button
            onClick={() => setIsAuthModalOpen(true)}
            className="group bg-indigo-600 hover:bg-indigo-700 text-white py-4 px-8 text-lg font-medium rounded-lg shadow-md hover:shadow-xl transition-all"
          >
            Join Waitlist
          </Button>
        </div>
      </div>

      <AuthModal open={isAuthModalOpen} onClose={() => setIsAuthModalOpen(false)} defaultRole="brand" />
    </section>
  );
}
