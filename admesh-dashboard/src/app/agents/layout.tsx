import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Agents page specific metadata
export const metadata: Metadata = generateMetadata(
  "Monetize Your AI Platform Through the AdMesh Protocol",
  "Integrate in minutes. Recommend verified offers. Earn when users take real action. AdMesh gives AI platforms a clean path to plug into verified advertiser offers and track every user action.",
  "/agents",
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
