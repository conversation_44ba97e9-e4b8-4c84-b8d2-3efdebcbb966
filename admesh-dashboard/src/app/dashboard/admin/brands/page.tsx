"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Search,
  ArrowUpDown,
  Mail,
  Building,
  Globe,
  DollarSign,
  ExternalLink
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface Brand {
  uid: string;
  company_name?: string;
  website?: string;
  logo_url?: string;
  work_email?: string;
  headquarters?: string;
  industry?: string;
  verified?: boolean;
  offers_submitted?: number;
  budget_used?: number;
  budget_remaining?: number;
  active_offers?: string[];
  active_products?: string[];
  inactive_offers?: string[];
  inactive_products?: string[];
  created_at?: {
    seconds: number;
    nanoseconds: number;
  } | string;
  updated_at?: {
    seconds: number;
    nanoseconds: number;
  } | string;
  onboarding_completed?: boolean;
}

type SortField = "company_name" | "created_at" | "offers_submitted" | "budget_used" | "budget_remaining" | "none";
type SortDirection = "asc" | "desc";

export default function AdminBrandsPage() {
  const { user, role, isAdmin } = useAuth();
  const router = useRouter();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalBrands, setTotalBrands] = useState(0);
  const [sortField, setSortField] = useState<SortField>("created_at");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const itemsPerPage = 50;

  // Redirect if not admin
  useEffect(() => {
    if (role && role !== "admin") {
      router.push("/dashboard");
    }
  }, [role, router]);

  // Fetch brands
  useEffect(() => {
    const fetchBrands = async () => {
      if (!user || !isAdmin) return;

      try {
        setLoading(true);
        setError(null);

        const token = await user.getIdToken();
        const offset = (currentPage - 1) * itemsPerPage;

        // Build query parameters
        const params = new URLSearchParams({
          limit: itemsPerPage.toString(),
          offset: offset.toString(),
          sort_by: sortField !== 'none' ? sortField : 'created_at',
          sort_direction: sortDirection,
        });

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/admin/all?${params.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch brands");
        }

        const data = await response.json();
        setBrands(data.brands || []);
        setTotalBrands(data.pagination?.total || 0);
        setTotalPages(Math.ceil((data.pagination?.total || 0) / itemsPerPage));
      } catch (err) {
        console.error("Error fetching brands:", err);
        setError("Failed to load brands. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchBrands();
  }, [user, isAdmin, currentPage, sortField, sortDirection, itemsPerPage]);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Filter brands by search query
  const filteredBrands = brands.filter((brand) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (brand.company_name?.toLowerCase().includes(searchLower) || false) ||
      (brand.website?.toLowerCase().includes(searchLower) || false) ||
      (brand.work_email?.toLowerCase().includes(searchLower) || false) ||
      (brand.industry?.toLowerCase().includes(searchLower) || false)
    );
  });

  // Format timestamp
  const formatTimestamp = (
    timestamp?: { seconds: number; nanoseconds: number } | string
  ) => {
    if (!timestamp) return "N/A";

    // Handle Firestore timestamp
    if (typeof timestamp === 'object' && 'seconds' in timestamp) {
      const date = new Date(timestamp.seconds * 1000);
      return formatDistanceToNow(date, { addSuffix: true });
    }

    // Handle string timestamp
    if (typeof timestamp === 'string') {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    }

    return "N/A";
  };

  // Format currency
  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined) return "$0.00";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // If not admin, show loading or redirect
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Admin: Advertisers</h2>
          <p className="text-muted-foreground mt-1">
            Manage all advertisers in the AdMesh platform.
          </p>
        </div>
        <div className="w-full sm:w-auto">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search advertisers..."
              className="w-full sm:w-[300px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Sorting Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Sort by:</span>
            <Select
              value={sortField}
              onValueChange={(value) => setSortField(value as SortField)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select field" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Date Joined</SelectItem>
                <SelectItem value="company_name">Company Name</SelectItem>
                <SelectItem value="offers_submitted">Offers</SelectItem>
                <SelectItem value="budget_used">Budget Used</SelectItem>
                <SelectItem value="budget_remaining">Budget Remaining</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setSortDirection(sortDirection === "asc" ? "desc" : "asc")}
            className="gap-1"
          >
            <ArrowUpDown className="h-4 w-4" />
            {sortDirection === "asc" ? "Ascending" : "Descending"}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-xl">All Brands</CardTitle>
          <CardDescription>
            Total: {totalBrands} brands | Showing {filteredBrands.length} brands | {itemsPerPage} per page
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">{error}</div>
          ) : filteredBrands.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No brands found.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("company_name")}
                    >
                      <div className="flex items-center gap-1">
                        <Building className="h-4 w-4" />
                        Company
                        {sortField === "company_name" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Website</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("offers_submitted")}
                    >
                      <div className="flex items-center gap-1">
                        Offers
                        {sortField === "offers_submitted" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("budget_used")}
                    >
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4" />
                        Budget Used
                        {sortField === "budget_used" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("created_at")}
                    >
                      <div className="flex items-center gap-1">
                        Joined
                        {sortField === "created_at" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBrands.map((brand) => (
                    <TableRow key={brand.uid}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col">
                          <span className="truncate max-w-[150px]">
                            {brand.company_name || "Unnamed Brand"}
                          </span>
                          {brand.industry && (
                            <span className="text-xs text-muted-foreground truncate max-w-[150px]">
                              {brand.industry}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {brand.website ? (
                          <a
                            href={brand.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1 text-primary hover:underline"
                          >
                            <Globe className="h-4 w-4" />
                            <span className="truncate max-w-[120px]">
                              {brand.website.replace(/^https?:\/\/(www\.)?/, '')}
                            </span>
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        ) : (
                          <span className="text-muted-foreground">No website</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {brand.work_email ? (
                          <a
                            href={`mailto:${brand.work_email}`}
                            className="flex items-center gap-1 text-primary hover:underline"
                          >
                            <Mail className="h-4 w-4" />
                            <span className="truncate max-w-[120px]">
                              {brand.work_email}
                            </span>
                          </a>
                        ) : (
                          <span className="text-muted-foreground">No email</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          <Badge
                            variant={brand.verified ? "default" : "secondary"}
                          >
                            {brand.verified ? "Verified" : "Unverified"}
                          </Badge>
                          <Badge
                            variant={brand.onboarding_completed ? "default" : "outline"}
                          >
                            {brand.onboarding_completed ? "Onboarded" : "Incomplete"}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>
                            {brand.offers_submitted || 0} submitted
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {brand.active_offers?.length || 0} active
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{formatCurrency(brand.budget_used)}</span>
                          <span className="text-xs text-muted-foreground">
                            Remaining: {formatCurrency(brand.budget_remaining)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatTimestamp(brand.created_at)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination className="mt-4">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  />
                </PaginationItem>

                {/* Show limited page numbers with ellipsis for large page counts */}
                {(() => {
                  // Always show first page
                  const pagesToShow = [1];

                  // Show pages around current page
                  const rangeStart = Math.max(2, currentPage - 1);
                  const rangeEnd = Math.min(totalPages - 1, currentPage + 1);

                  // Add ellipsis indicator if needed before current range
                  if (rangeStart > 2) {
                    pagesToShow.push(-1); // -1 represents ellipsis
                  }

                  // Add pages around current page
                  for (let i = rangeStart; i <= rangeEnd; i++) {
                    pagesToShow.push(i);
                  }

                  // Add ellipsis indicator if needed after current range
                  if (rangeEnd < totalPages - 1) {
                    pagesToShow.push(-2); // -2 represents ellipsis (different key)
                  }

                  // Always show last page if more than 1 page
                  if (totalPages > 1) {
                    pagesToShow.push(totalPages);
                  }

                  return pagesToShow.map((page) => (
                    <PaginationItem key={page}>
                      {page < 0 ? (
                        <span className="px-4">...</span>
                      ) : (
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                        >
                          {page}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ));
                })()}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
